const axios = require('axios');

async function testCriticalFixes() {
    console.log('Testing critical fixes...\n');
    
    try {
        // Test 1: Create a simple mission that should NOT create multiple agents
        console.log('1. Testing agent creation (should not create multiple agents)...');
        const missionResponse = await axios.post('http://localhost:5020/createMission', {
            goal: 'Ask me what my favorite color is and save the answer to a file'
        });
        
        if (missionResponse.status === 200) {
            const missionId = missionResponse.data.missionId;
            console.log(`✓ Mission created: ${missionId}`);
            
            // Wait a bit for processing
            await new Promise(resolve => setTimeout(resolve, 10000));
            
            // Check how many agents were created
            const agentsResponse = await axios.get(`http://localhost:5050/missions/${missionId}/agents`);
            const agentCount = agentsResponse.data.agents ? agentsResponse.data.agents.length : 0;
            
            console.log(`   Agents created: ${agentCount}`);
            if (agentCount <= 2) {
                console.log('✓ Agent creation looks reasonable (≤2 agents)');
            } else {
                console.log('✗ Too many agents created');
            }
            
            // Test 2: Check if Python plugins are working by looking at logs
            console.log('\n2. Checking if Python plugins are working...');
            
            // Wait a bit more for plugin execution
            await new Promise(resolve => setTimeout(resolve, 5000));
            
            console.log('✓ Test completed. Check logs for Python execution results.');
            
        } else {
            console.log('✗ Failed to create mission');
        }
        
    } catch (error) {
        console.error('Test failed:', error.message);
    }
}

testCriticalFixes();
