const axios = require('axios');

async function testPythonExecution() {
    console.log('Testing Python plugin execution...\n');
    
    try {
        // Test 1: Create a simple mission that uses ASK_USER_QUESTION
        console.log('1. Testing ASK_USER_QUESTION plugin execution...');
        const missionResponse = await axios.post('http://localhost:5020/createMission', {
            goal: 'Ask me what my favorite color is'
        });
        
        if (missionResponse.status === 200) {
            const missionId = missionResponse.data.missionId;
            console.log(`✓ Mission created: ${missionId}`);
            
            // Wait for processing
            await new Promise(resolve => setTimeout(resolve, 15000));
            
            console.log('✓ Test completed. Check logs for Python execution results.');
            
        } else {
            console.log('✗ Failed to create mission');
        }
        
    } catch (error) {
        console.error('Test failed:', error.message);
    }
}

testPythonExecution();
