const axios = require('axios');

async function testFileOperation() {
    console.log('Testing FILE_OPERATION plugin execution...\n');
    
    try {
        // Test: Create a mission that writes to a file
        console.log('1. Testing FILE_OPERATION plugin with write operation...');
        const missionResponse = await axios.post('http://localhost:5020/createMission', {
            goal: 'Write "Hello World" to a file called test.txt'
        });
        
        if (missionResponse.status === 200) {
            const missionId = missionResponse.data.missionId;
            console.log(`✓ Mission created: ${missionId}`);
            
            // Wait for processing
            console.log('Waiting for mission to process...');
            await new Promise(resolve => setTimeout(resolve, 20000));
            
            console.log('✓ Test completed. Check logs for FILE_OPERATION execution results.');
            
        } else {
            console.log('✗ Failed to create mission');
        }
        
    } catch (error) {
        console.error('Test failed:', error.message);
        if (error.response) {
            console.error('Response status:', error.response.status);
            console.error('Response data:', error.response.data);
        }
    }
}

testFileOperation();
