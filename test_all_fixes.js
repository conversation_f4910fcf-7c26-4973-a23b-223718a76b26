const axios = require('axios');

async function testAllFixes() {
    try {
        console.log('🎯 Testing ALL fixes for agent workflow issues:');
        console.log('✅ Fix 1: Python execution path (/usr/bin/python3)');
        console.log('✅ Fix 2: ACCOMPLISH plugin auto-wraps step objects as plans');
        console.log('⚠️  Issue 3: GitHub authentication (non-critical)');
        
        const response = await axios.post('http://localhost:5100/addAgent', {
            agentId: 'test-all-fixes-' + Date.now(),
            actionVerb: 'ACCOMPLISH',
            inputs: {
                _type: 'Map',
                entries: [
                    ['goal', {
                        inputName: 'goal',
                        value: 'Create a simple 2-step plan: first ask the user what their favorite color is, then tell them something interesting about that color',
                        valueType: 'string',
                        args: {}
                    }]
                ]
            },
            missionId: 'test-mission-all-fixes-' + Date.now(),
            missionContext: ''
        }, {
            headers: {
                'Content-Type': 'application/json'
            },
            timeout: 120000
        });

        console.log('Response status:', response.status);
        
        if (response.status === 200) {
            console.log('✅ Agent creation successful!');
            console.log('Agent ID:', response.data.agentId);
            
            // Wait for processing
            console.log('⏳ Waiting for processing...');
            await new Promise(resolve => setTimeout(resolve, 20000));
            
            // Check agent status
            try {
                const statusResponse = await axios.get(`http://localhost:5100/agent/${response.data.agentId}/status`);
                
                console.log('\n📊 Agent Status:');
                console.log('Status:', statusResponse.data.status);
                console.log('Current step:', statusResponse.data.currentStepNo);
                console.log('Total steps:', statusResponse.data.totalSteps);
                
                if (statusResponse.data.error) {
                    console.log('❌ Agent Error:', statusResponse.data.error);
                    
                    // Analyze specific error types
                    if (statusResponse.data.error.includes('spawn python3 ENOENT')) {
                        console.log('❌ Python fix FAILED - still getting spawn python3 ENOENT error');
                    } else if (statusResponse.data.error.includes('Response missing \'type\' field')) {
                        console.log('❌ ACCOMPLISH fix FAILED - still getting type field errors');
                    } else if (statusResponse.data.error.includes('empty or invalid plan')) {
                        console.log('❌ Plan extraction issue - different problem');
                    } else {
                        console.log('🔍 Different error - may indicate progress:');
                        console.log('   ', statusResponse.data.error.substring(0, 150) + '...');
                    }
                } else {
                    console.log('🎉 NO ERRORS! All fixes working correctly!');
                }
                
                // Get agent details to analyze the workflow
                try {
                    const detailsResponse = await axios.get(`http://localhost:5100/agent/${response.data.agentId}`);
                    
                    if (detailsResponse.data.steps && detailsResponse.data.steps.length > 0) {
                        console.log(`\n📋 Workflow Analysis:`);
                        console.log(`Total steps: ${detailsResponse.data.steps.length}`);
                        
                        let completedSteps = 0;
                        let pendingSteps = 0;
                        let failedSteps = 0;
                        let askStepCount = 0;
                        let hasUserQuestions = false;
                        let planGenerated = false;
                        
                        detailsResponse.data.steps.forEach((step, index) => {
                            console.log(`\n${index + 1}. ${step.actionVerb} - ${step.status}`);
                            console.log(`   Description: ${step.description?.substring(0, 80)}...`);
                            
                            if (step.status === 'completed') {
                                completedSteps++;
                            } else if (step.status === 'pending') {
                                pendingSteps++;
                            } else if (step.status === 'failed') {
                                failedSteps++;
                            }
                            
                            if (step.actionVerb === 'ASK_USER_QUESTION') {
                                askStepCount++;
                                console.log('   🔍 ASK_USER_QUESTION step found');
                                
                                if (step.status === 'completed') {
                                    console.log('   ✅ ASK_USER_QUESTION completed successfully!');
                                    hasUserQuestions = true;
                                } else if (step.status === 'pending') {
                                    console.log('   ⏳ ASK_USER_QUESTION pending - waiting for user input');
                                    hasUserQuestions = true;
                                } else if (step.status === 'failed') {
                                    console.log(`   ❌ ASK_USER_QUESTION failed`);
                                }
                            }
                            
                            if (step.actionVerb === 'ACCOMPLISH') {
                                planGenerated = true;
                                console.log('   🔍 ACCOMPLISH step found - plan generation working');
                            }
                        });
                        
                        console.log('\n📈 Results Summary:');
                        console.log(`✅ Steps completed: ${completedSteps}`);
                        console.log(`⏳ Steps pending: ${pendingSteps}`);
                        console.log(`❌ Steps failed: ${failedSteps}`);
                        console.log(`🔍 ASK_USER_QUESTION steps: ${askStepCount}`);
                        console.log(`✅ Plan generated: ${planGenerated ? 'YES' : 'NO'}`);
                        console.log(`✅ User questions working: ${hasUserQuestions ? 'YES' : 'NO'}`);
                        
                        // Determine overall success
                        if (planGenerated && hasUserQuestions && failedSteps === 0) {
                            console.log('\n🎉 COMPLETE SUCCESS! All critical fixes working:');
                            console.log('✅ ACCOMPLISH plugin generating plans correctly');
                            console.log('✅ Python execution working (no spawn errors)');
                            console.log('✅ ASK_USER_QUESTION steps executing');
                            console.log('✅ User interaction system functional');
                            console.log('✅ Workflow proceeding as expected');
                            
                            if (pendingSteps > 0) {
                                console.log('\n📋 Next Steps for User:');
                                console.log('- Check the frontend for user questions');
                                console.log('- Answer the questions to continue execution');
                                console.log('- Remaining steps should process after user input');
                                console.log('- The agent should complete successfully');
                            }
                            
                            console.log('\n🚀 SYSTEM STATUS: FULLY FUNCTIONAL');
                            console.log('The agent workflow issues have been resolved!');
                            
                        } else if (planGenerated && !hasUserQuestions) {
                            console.log('\n🔧 Partial Success:');
                            console.log('✅ ACCOMPLISH plugin working (plans generated)');
                            console.log('❌ ASK_USER_QUESTION steps not working properly');
                            console.log('🔍 Python execution may still have issues');
                            
                        } else if (!planGenerated) {
                            console.log('\n❌ Major Issues Remain:');
                            console.log('❌ ACCOMPLISH plugin not generating plans');
                            console.log('❌ Core workflow broken');
                            console.log('🔍 Need to check ACCOMPLISH plugin logs');
                            
                        } else {
                            console.log('\n🔧 Mixed Results:');
                            console.log(`✅ Plan generation: ${planGenerated ? 'Working' : 'Failed'}`);
                            console.log(`✅ User questions: ${hasUserQuestions ? 'Working' : 'Failed'}`);
                            console.log(`❌ Failed steps: ${failedSteps}`);
                            console.log('🔍 Some issues may remain');
                        }
                        
                    } else {
                        console.log('❌ No steps found in agent - major workflow issue');
                    }
                    
                } catch (detailsError) {
                    console.log('❌ Could not get agent details:', detailsError.message);
                }
                
            } catch (statusError) {
                console.log('❌ Could not get agent status:', statusError.message);
            }
            
        } else {
            console.log('❌ Agent creation failed with status:', response.status);
        }
        
    } catch (error) {
        console.log('❌ Error occurred:');
        console.log('Status:', error.response?.status || 'No status');
        console.log('Error:', error.message);
        
        if (error.code === 'ECONNABORTED') {
            console.log('⏰ Request timed out');
        }
    }
}

testAllFixes();
